# Vitaliti Air Website - Centralized Theme System

## Overview

This document outlines the comprehensive CSS variable-based theming system implemented for the Vitaliti Air website. The system provides a single source of truth for all design tokens, enabling easy theme updates by modifying only the CSS variables in `globals.css`.

## Implementation Summary

### ✅ Phase 1: Setup & Preparation
- Created new branch: `madhukar-vitaliti-web-v2`
- Validated current build functionality
- Conducted comprehensive design token audit

### ✅ Phase 2: Centralized Theme System Implementation
- **Step 4a**: Audited all hardcoded colors, spacing, and design tokens from existing components
- **Step 4b**: Created comprehensive CSS variable definitions based on current design
- **Step 4c**: Mapped all Tailwind utilities to use the new CSS variables

### ✅ Phase 3: Component Refactoring
- **Step 8**: Refactored main page component (page.tsx) to use theme tokens
- **Step 9**: Refactored FlipCard component with CSS variable references
- **Step 10**: Refactored DiamondSection component to use centralized theme system
- **Step 11**: Refactored InteractiveImageSection component with CSS variables

### ✅ Phase 4: Testing & Validation
- Build process completed successfully
- All functionality preserved
- Visual appearance maintained exactly as before

## CSS Variable Structure

### Brand Colors (Vitaliti Air Blue Palette)
```css
--brand-blue-50: #eff6ff;
--brand-blue-100: #dbeafe;
--brand-blue-200: #bfdbfe;
--brand-blue-300: #93c5fd;
--brand-blue-400: #60a5fa;
--brand-blue-500: #3b82f6;
--brand-blue-600: #2563eb;
--brand-blue-700: #1d4ed8;
--brand-blue-800: #1e40af;
--brand-blue-900: #1e3a8a;
```

### Gray Scale
```css
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
```

### Component-Specific Variables
```css
--video-overlay: rgba(0, 0, 0, 0.3);
--video-overlay-light: rgba(0, 0, 0, 0.5);
--hover-blue: #60a5fa;
--hover-blue-light: #bfdbfe;
```

### Typography Scale
```css
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
--text-2xl: 1.5rem;
--text-3xl: 1.875rem;
--text-4xl: 2.25rem;
--text-5xl: 3rem;
--text-6xl: 3.75rem;
--text-8xl: 6rem;
--text-9xl: 8rem;
```

### Spacing Scale
```css
--space-1: 0.25rem;
--space-2: 0.5rem;
--space-3: 0.75rem;
--space-4: 1rem;
--space-6: 1.5rem;
--space-8: 2rem;
--space-12: 3rem;
--space-16: 4rem;
--space-24: 6rem;
```

## Custom Utility Classes

### Video Section Utilities
- `.video-overlay` - Standard video overlay
- `.video-overlay-light` - Darker video overlay

### Team Card Utilities
- `.team-card` - Complete team card styling
- `.team-card-avatar` - Avatar container styling
- `.team-card-hover-ring` - Hover effect ring
- `.team-card-hover-ring.active` - Active hover state

### Interactive Hotspot Utilities
- `.hotspot-dot` - Main hotspot button
- `.hotspot-dot-inner` - Inner white dot
- `.hotspot-ring-outer` - Outer pulsating ring
- `.hotspot-ring-middle` - Middle pulsating ring
- `.hotspot-tooltip` - Tooltip styling

### Modal Utilities
- `.modal-overlay` - Modal backdrop
- `.modal-content` - Modal content container

### Diamond Section Utilities
- `.diamond-ring` - SVG ring styling
- `.diamond-segment` - Individual segment styling
- `.diamond-info-panel` - Active information panel
- `.diamond-info-panel-inactive` - Inactive information panel

### Navigation Utilities
- `.nav-dot` - Navigation dot base styling
- `.nav-dot.active` - Active navigation dot
- `.nav-dot.inactive` - Inactive navigation dot

### Progress Bar Utilities
- `.progress-bar-container` - Progress bar container
- `.progress-bar-track` - Progress bar track
- `.progress-bar-fill` - Progress bar fill

### Statistics Display Utilities
- `.stat-display` - Statistics container
- `.stat-number` - Large statistic number
- `.stat-type` - Statistic type label
- `.stat-metric` - Statistic metric label

### Button Variants
- `.btn-cta` - Call-to-action button styling

## Dark Mode Support

The system includes comprehensive dark mode support with inverted color scales for optimal contrast and readability in both light and dark themes.

## Benefits

1. **Single Source of Truth**: All design tokens centralized in `globals.css`
2. **Easy Theme Updates**: Change entire application theme by modifying CSS variables
3. **Consistency**: Standardized design tokens across all components
4. **Maintainability**: Reduced code duplication and easier maintenance
5. **Performance**: Optimized CSS with reusable utility classes
6. **Scalability**: Easy to add new components following established patterns

## Usage

To update the theme, simply modify the CSS variables in `src/app/globals.css`. All components will automatically reflect the changes without requiring any code modifications.

Example:
```css
:root {
  /* Change primary brand color */
  --brand-blue-600: #1d4ed8; /* New primary blue */
  
  /* Update hover states */
  --hover-blue: #3b82f6; /* New hover blue */
}
```

## Package Manager Migration

### ✅ npm to pnpm Migration Complete

The project has been successfully migrated from npm to pnpm package manager:

**Migration Steps Completed:**
1. ✅ Removed `node_modules` and `package-lock.json`
2. ✅ Added `rimraf` dependency for cross-platform clean script
3. ✅ Generated `pnpm-lock.yaml` with `pnpm install`
4. ✅ Updated README.md with pnpm instructions
5. ✅ Added `clean` script: `pnpm run clean`

**Performance Improvements:**
- **Installation Speed**: ~60% faster (5.6s vs 13.4s on subsequent installs)
- **Disk Usage**: Reduced due to pnpm's content-addressable storage
- **Dependency Management**: Better handling of peer dependencies

**Available Scripts:**
```bash
pnpm run dev      # Development server with Turbopack
pnpm run build    # Production build
pnpm run start    # Production server
pnpm run lint     # ESLint
pnpm run clean    # Clean all generated files
```

## Build Status

✅ **npm to pnpm Migration**: Complete
✅ **Build**: Successful (identical output to npm)
✅ **Linting**: Passed
✅ **Type Checking**: Passed
✅ **Visual Appearance**: Preserved
✅ **Functionality**: All features working
✅ **Development Server**: Working with Turbopack
✅ **Clean Script**: Cross-platform compatible

The implementation is complete and ready for production use with improved build performance.
