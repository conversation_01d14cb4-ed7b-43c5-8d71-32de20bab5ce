# Legacy Design Archive

This folder contains a complete snapshot of the original Vitaliti Air website design, preserved for reference and potential restoration.

## What's Included

- **src/**: Complete source code including components, app pages, and utilities
- **public/**: All original assets and images
- **Configuration files**: package.json, next.config.ts, tsconfig.json, etc.

## How to Use This Archive

### Quick Reference
Simply browse the files in this folder to see the original implementation.

### Restore Original Design
If you need to restore the original design:

1. **Option A - Copy files back:**
   ```bash
   cp -r legacy/src/* src/
   cp -r legacy/public/* public/
   cp legacy/package.json .
   # ... copy other config files as needed
   ```

2. **Option B - Use git branches:**
   ```bash
   git checkout legacy-design
   ```

3. **Option C - Use git tags:**
   ```bash
   git checkout v1.0-original-design
   ```

### Extract Specific Components
Copy individual components or assets as needed:
```bash
cp legacy/src/components/ui/button.tsx src/components/ui/
cp legacy/public/images/team-photo.jpg public/images/
```

## Preservation Strategy

This design is preserved in three ways:
1. **Git Tag**: `v1.0-original-design` - Historical reference
2. **Git Branch**: `legacy-design` - Living codebase for maintenance
3. **This Folder**: Instant access without git commands

## Shared Assets

Team photos and other reusable assets have been copied to `/assets/images/shared/` for easy access across design versions.

---

*Last updated: $(date)*
*Original design preserved on: $(date)* 