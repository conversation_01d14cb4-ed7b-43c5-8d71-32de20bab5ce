import React from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import {
  Brain,
  Heart,
  Wind,
  TrendingUp,
  BarChart3,
  ArrowRight,
  CheckCircle,
} from "lucide-react"
import Link from "next/link"

export default function VitalitiAirLanding() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Wind className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900">Vitaliti Air</span>
          </div>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="#results" className="text-gray-600 hover:text-blue-600 transition-colors font-medium">Results</Link>
            <Link href="#how-it-works" className="text-gray-600 hover:text-blue-600 transition-colors font-medium">How It Works</Link>
            <Link href="#science" className="text-gray-600 hover:text-blue-600 transition-colors font-medium">Science</Link>
            <Link href="#team" className="text-gray-600 hover:text-blue-600 transition-colors font-medium">Team</Link>
            <Link href="#faq" className="text-gray-600 hover:text-blue-600 transition-colors font-medium">FAQ</Link>
          </nav>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">Join Waitlist</Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-b from-blue-50 to-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center justify-center gap-12">
            {/* Left: Text Content */}
            <div className="flex-1 max-w-xl text-center lg:text-left">
              <Badge className="mb-8 bg-blue-100 text-blue-800 border-blue-200 px-4 py-2">
                Revolutionary Altitude Training Technology
              </Badge>
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
                Enjoy the Benefits of <span className="text-blue-600">Exercise</span>{" "}
                <span className="italic text-gray-600 font-normal">without Exercise</span>
              </h1>
              <p className="text-xl lg:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                Boost mental clarity, accelerate metabolism, and enhance longevity with our smart altitude training device.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center mb-16">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold">
                  Sign Up Now <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </div>
            </div>
            {/* Right: Device Image */}
            <div className="flex-1 flex flex-col items-center justify-center">
              <Image src="/images/vitaliti-air-device.png" alt="Vitaliti Air Device" width={400} height={400} className="rounded-2xl shadow-xl object-cover w-full max-w-xs lg:max-w-md" />
              <div className="mt-6 w-full flex justify-center">
                <span className="block text-lg text-gray-600 font-medium text-center max-w-xs">Sit. Breathe. Improve.</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Proven Results Section */}
      <section id="results" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">Results</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real stories from people using Vitaliti Air
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="shadow-lg border-gray-200">
              <CardContent className="p-8 text-center flex flex-col items-center justify-between min-h-[260px]">
                <Image src="/images/person1.png" alt="Michael T." width={64} height={64} className="rounded-full object-cover mb-4" />
                <p className="text-xl italic text-gray-800 mb-6">&apos;A few weeks in, my knee pain eased, the brace went into storage, and my doctor even reduced my BP meds. I&apos;ve got steady energy all day.&apos;</p>
                <div className="font-semibold text-blue-700 mt-auto">Michael T.</div>
              </CardContent>
            </Card>
            {/* Testimonial 2 */}
            <Card className="shadow-lg border-gray-200">
              <CardContent className="p-8 text-center flex flex-col items-center justify-between min-h-[260px]">
                <Image src="/images/person2.png" alt="Sarah L." width={64} height={64} className="rounded-full object-cover mb-4" />
                <p className="text-xl italic text-gray-800 mb-6">&apos;My VO₂ Max increased by 14 points—I sail up the stairs now without getting winded. Can&apos;t wait to see if I can keep improving.&apos;</p>
                <div className="font-semibold text-blue-700 mt-auto">Sarah L.</div>
              </CardContent>
            </Card>
            {/* Testimonial 3 */}
            <Card className="shadow-lg border-gray-200">
              <CardContent className="p-8 text-center flex flex-col items-center justify-between min-h-[260px]">
                <Image src="/images/person3.png" alt="Amit K." width={64} height={64} className="rounded-full object-cover mb-4" />
                <p className="text-xl italic text-gray-800 mb-6">&apos;I dropped eight pounds without changing my diet, feel more energized, and friends keep noticing I look leaner.&apos;</p>
                <div className="font-semibold text-blue-700 mt-auto">Amit K.</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Three simple steps to unlock your body&apos;s potential through smart altitude training
            </p>
          </div>
          <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white relative overflow-hidden">
              <div className="absolute top-6 left-6">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">1</span>
                </div>
              </div>
              <CardHeader className="text-center pt-8 pb-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto flex items-center justify-center mb-6">
                  <Wind className="w-12 h-12 text-blue-600" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Breathe</CardTitle>
              </CardHeader>
              <CardContent className="text-center px-6 pb-8">
                <p className="text-gray-600 text-lg leading-relaxed">
                  Simply breathe through our advanced device as it creates precise altitude conditions tailored to your
                  physiology.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white relative overflow-hidden">
              <div className="absolute top-6 left-6">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">2</span>
                </div>
              </div>
              <CardHeader className="text-center pt-8 pb-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto flex items-center justify-center mb-6">
                  <TrendingUp className="w-12 h-12 text-blue-600" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Adapt</CardTitle>
              </CardHeader>
              <CardContent className="text-center px-6 pb-8">
                <p className="text-gray-600 text-lg leading-relaxed">
                  Your body naturally adapts to the controlled hypoxic environment, triggering beneficial physiological
                  responses.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white relative overflow-hidden">
              <div className="absolute top-6 left-6">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">3</span>
                </div>
              </div>
              <CardHeader className="text-center pt-8 pb-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto flex items-center justify-center mb-6">
                  <BarChart3 className="w-12 h-12 text-blue-600" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Track</CardTitle>
              </CardHeader>
              <CardContent className="text-center px-6 pb-8">
                <p className="text-gray-600 text-lg leading-relaxed">
                  Monitor your progress with real-time biometric tracking and personalized insights through our smart
                  app.
                </p>
              </CardContent>
            </Card>
          </div>
          <div className="text-center mt-12">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">Reserve Your Device</Button>
          </div>
        </div>
      </section>

      {/* Science Section */}
      <section id="science" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">The Science</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Backed by decades of research in altitude physiology and hypoxic conditioning
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <Card className="border-gray-200 shadow-lg bg-white min-h-[240px]">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <BarChart3 className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle className="text-gray-900">Improved Fitness</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col h-full">
                <p className="text-gray-600 mb-4 leading-relaxed">
                  Increases VO₂ Max by an average of 3.2 mL/kg/min.
                </p>
                <span className="mt-auto">
                  <a href="https://link.springer.com/article/10.1186/s13102-023-00784-3" target="_blank" rel="noopener noreferrer">
                    <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50 mb-2 hover:bg-blue-100 transition-colors cursor-pointer">2023 Meta-Analysis</Badge>
                  </a>
                </span>
              </CardContent>
            </Card>
            <a href="https://journals.sagepub.com/doi/10.3233/JAD-240711?url_ver=Z39.88-2003&rfr_id=ori:rid:crossref.org&rfr_dat=cr_pub%20%200pubmed" target="_blank" rel="noopener noreferrer" className="block hover:shadow-xl transition-shadow rounded-2xl">
              <Card className="border-gray-200 shadow-lg bg-white min-h-[240px]">
                <CardHeader>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <Brain className="w-6 h-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-gray-900">Cognitive Enhancement</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col h-full">
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Improves cognition and cerebral tissue oxygen saturation, blood flow, and vascular conductance.
                  </p>
                  <span className="mt-auto">
                    <a href="https://link.springer.com/article/10.1007/s00421-020-04410-9" target="_blank" rel="noopener noreferrer">
                      <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50 mb-2 hover:bg-blue-100 transition-colors cursor-pointer">2024 Systematic Review</Badge>
                    </a>
                  </span>
                </CardContent>
              </Card>
            </a>
            <Card className="border-gray-200 shadow-lg bg-white min-h-[240px]">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle className="text-gray-900">Cardiovascular Benefits</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col h-full">
                <p className="text-gray-600 mb-4 leading-relaxed">
                  Decreases blood pressure by 10 mmHg in 6 weeks.
                </p>
                <span className="mt-auto">
                  <a href="https://link.springer.com/article/10.1007/s00421-020-04410-9" target="_blank" rel="noopener noreferrer">
                    <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50 mb-2 hover:bg-blue-100 transition-colors cursor-pointer">2020 Clinical Trial</Badge>
                  </a>
                </span>
              </CardContent>
            </Card>
          </div>
          <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Key Mechanisms</h3>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Mitochondrial Biogenesis</h4>
                  <p className="text-gray-600 leading-relaxed">
                    Controlled hypoxia stimulates the production of new mitochondria, enhancing cellular energy
                    production and metabolic efficiency.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">HIF-1α Activation</h4>
                  <p className="text-gray-600 leading-relaxed">
                    Hypoxia-inducible factor activation triggers adaptive responses that improve oxygen utilization and
                    cellular resilience.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">VEGF Upregulation</h4>
                  <p className="text-gray-600 leading-relaxed">
                    Increased vascular endothelial growth factor promotes angiogenesis and improved blood flow
                    throughout the body.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">EPO Production</h4>
                  <p className="text-gray-600 leading-relaxed">
                    Natural erythropoietin production enhances oxygen-carrying capacity and overall cardiovascular
                    performance.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="text-center mt-12">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">Improve my Health</Button>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section id="team" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">World-Class Team</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Led by renowned experts in systems biology, cardiovascular medicine, and longevity research
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="border-gray-200 shadow-lg bg-white">
              <CardHeader className="text-center">
                <Image src="/images/nathan-price.jpeg" alt="Nathan Price, PhD" width={96} height={96} className="rounded-full mx-auto mb-6 object-cover w-24 h-24" />
                <CardTitle className="text-gray-900 text-xl mb-2">Nathan Price, PhD</CardTitle>
                <CardDescription className="text-blue-600 font-semibold text-base">
                  Chief Science Officer
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 leading-relaxed">
                  Professor at Institute for Systems Biology. Pioneer in scientific wellness.
                </p>
              </CardContent>
            </Card>
            <Card className="border-gray-200 shadow-lg bg-white">
              <CardHeader className="text-center">
                <Image src="/images/stanley-rockson.jpg" alt="Stanley G. Rockson, MD" width={96} height={96} className="rounded-full mx-auto mb-6 object-cover w-24 h-24" />
                <CardTitle className="text-gray-900 text-xl mb-2">Stanley G. Rockson, MD</CardTitle>
                <CardDescription className="text-blue-600 font-semibold text-base">
                  Chief Medical Advisor
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 leading-relaxed">
                  Professor of Cardiovascular Medicine at Stanford. Leader in vascular biology.
                </p>
              </CardContent>
            </Card>
            <Card className="border-gray-200 shadow-lg bg-white">
              <CardHeader className="text-center">
                <Image src="/images/brian-kennedy.jpeg" alt="Brian Kennedy, PhD" width={96} height={96} className="rounded-full mx-auto mb-6 object-cover w-24 h-24" />
                <CardTitle className="text-gray-900 text-xl mb-2">Brian Kennedy, PhD</CardTitle>
                <CardDescription className="text-blue-600 font-semibold text-base">
                  Scientific Advisor, Longevity
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 leading-relaxed">
                  Director of Centre for Healthy Longevity at NUS. Authority on aging biology.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to know about Vitaliti Air</p>
          </div>
          <div className="max-w-4xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem value="item-1" className="bg-white rounded-lg border border-gray-200 px-6 shadow-sm">
                <AccordionTrigger className="text-gray-900 hover:text-blue-600 font-medium">
                  How does Vitaliti Air work?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed">
                  The device guides you through short cycles of low-oxygen (hypoxia) and normal-oxygen (normoxia) breathing. This &quot;intermittent hypoxic-hyperoxic training&quot; triggers cellular and cardiovascular adaptations similar to high-altitude workouts—without leaving home.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="bg-white rounded-lg border border-gray-200 px-6 shadow-sm">
                <AccordionTrigger className="text-gray-900 hover:text-blue-600 font-medium">
                  Is it safe to let my blood-oxygen (SpO₂) dip into the low 80 % range?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed">
                  Yes, the protocol has been studied in thousands of sessions; transient dips to ~80 % are well-tolerated in healthy adults. Our built-in sensors pause the session if values fall outside the safe window. If you have heart, lung, or other medical conditions, we recommend consulting your physician first.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="bg-white rounded-lg border border-gray-200 px-6 shadow-sm">
                <AccordionTrigger className="text-gray-900 hover:text-blue-600 font-medium">
                  What benefits can I expect?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed">
                  Most users report measurable VO₂ max gains, higher day-to-day energy, and faster workout recovery within 4–6 weeks. Emerging studies also show promise for metabolic health, sleep quality, and long-COVID symptom relief. (Individual results vary.)
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="bg-white rounded-lg border border-gray-200 px-6 shadow-sm">
                <AccordionTrigger className="text-gray-900 hover:text-blue-600 font-medium">
                  How soon will I feel or see results?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed">
                  Many notice better stamina after 6–8 sessions; VO₂ max improvements typically become clear after 4–6 weeks of three 35-minute sessions per week.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="bg-white rounded-lg border border-gray-200 px-6 shadow-sm">
                <AccordionTrigger className="text-gray-900 hover:text-blue-600 font-medium">
                  When will pricing be announced, and how do I reserve a unit?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed">
                  We&apos;re finalizing manufacturing costs now. Join the waitlist—it&apos;s free and holds your spot. You&apos;ll get first-look pricing and an exclusive preorder discount before public release.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">Ready to Transform Your Health?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of early adopters who are already experiencing the future of wellness technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <div className="w-full max-w-md">
                <form className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 bg-white border-white text-gray-900 placeholder:text-gray-500"
                  />
                  <Button className="bg-white text-blue-600 hover:bg-gray-100 font-semibold">Sign Up Now</Button>
                </form>
              </div>
            </div>
            <p className="text-sm text-blue-200">
              Be the first to know when Vitaliti Air launches. No spam, unsubscribe anytime.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
