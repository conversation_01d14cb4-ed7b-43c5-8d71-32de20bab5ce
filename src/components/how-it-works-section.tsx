"use client"

import { getSectionContent, HowItWorksContent } from "@/lib/content"

export function HowItWorksSection() {
  const content = getSectionContent(4) as HowItWorksContent

  return (
    <section className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center" style={{ backgroundColor: 'var(--color-white)' }}>
      <div className="max-w-6xl mx-auto w-full">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-16" style={{ color: 'var(--color-gray-900)' }}>
          {content.title}
        </h2>
        
        <div className="space-y-16 md:space-y-24">
          {content.steps.map((step, index) => (
            <div key={step.number} className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8 md:gap-16`}>
              {/* Step Content */}
              <div className="flex-1 text-center md:text-left">
                <div className="step-number-container mb-6">
                  <div className="step-number">
                    {step.number}
                  </div>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold mb-4" style={{ color: 'var(--color-gray-900)' }}>
                  {step.title}
                </h3>
                <p className="text-lg leading-relaxed" style={{ color: 'var(--color-gray-600)' }}>
                  {step.text}
                </p>
              </div>

              {/* Step Image Placeholder */}
              <div className="flex-1">
                <div className="step-image-container">
                  <div className="step-image-placeholder">
                    <div className="image-icon">
                      {step.number === 1 && (
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                      {step.number === 2 && (
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0-6 0" stroke="currentColor" strokeWidth="2"/>
                          <path d="M17.5 12c0 4.5-3.5 8-5.5 8s-5.5-3.5-5.5-8c0-4.5 3.5-8 5.5-8s5.5 3.5 5.5 8" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                      )}
                      {step.number === 3 && (
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M3 3v18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="m19 9-5 5-4-4-3 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <p className="text-sm mt-4" style={{ color: 'var(--color-gray-500)' }}>
                      {step.image}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <style jsx>{`
        .step-number-container {
          display: flex;
          justify-content: center;
        }
        
        @media (min-width: 768px) {
          .step-number-container {
            justify-content: flex-start;
          }
        }
        
        .step-number {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--color-brand-blue-600), var(--color-brand-blue-700));
          color: var(--color-white);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          font-weight: bold;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .step-image-container {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
        }
        
        .step-image-placeholder {
          width: 300px;
          height: 300px;
          border-radius: 1rem;
          background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
          border: 2px dashed var(--color-gray-300);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          padding: 2rem;
          transition: all 0.3s ease;
        }
        
        .step-image-placeholder:hover {
          border-color: var(--color-brand-blue-400);
          background: linear-gradient(135deg, var(--color-brand-blue-50), var(--color-brand-blue-100));
        }
        
        .image-icon {
          color: var(--color-brand-blue-600);
          margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
          .step-image-placeholder {
            width: 250px;
            height: 250px;
            padding: 1.5rem;
          }
          
          .step-number {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
          }
        }
      `}</style>
    </section>
  )
}
