"use client"

import { useState } from "react"

export function DiamondSection() {
  const [activeSegment, setActiveSegment] = useState<number | null>(null)

  const segments = [
    {
      number: 1,
      title: "Hypoxic",
      description: "During the hypoxic phase, your body experiences carefully controlled low-oxygen conditions that mimic high-altitude environments. This oxygen restriction activates powerful cellular signaling pathways, triggering your cells to become more efficient and initiating the natural process of cellular renewal.",
      position: "top",
    },
    {
      number: 2,
      title: "Mitophagy",
      description: "Mitophagy is your body's natural quality control system. During this phase, damaged and dysfunctional mitochondria are identified and safely removed from your cells. This cellular housekeeping is essential for maintaining optimal energy production and preventing cellular damage that leads to fatigue and metabolic dysfunction.",
      position: "right",
    },
    {
      number: 3,
      title: "Hyperoxic",
      description: "The hyperoxic phase delivers oxygen-rich recovery periods that supercharge your cellular repair mechanisms. This controlled high-oxygen environment provides the optimal conditions for your cells to rebuild and strengthen, maximizing the benefits gained during the hypoxic phase.",
      position: "bottom",
    },
    {
      number: 4,
      title: "Mitochondrial Genesis",
      description: "The final phase stimulates mitochondrial genesis - the creation of brand new, highly efficient mitochondria. These fresh powerhouses replace the old ones removed during mitophagy, dramatically improving your cellular energy production capacity and leading to the measurable improvements in insulin sensitivity, reduced fatigue, and enhanced physical performance.",
      position: "left",
    },
  ]

  // Calculate path for each segment (90 degrees each, starting from top)
  const createSegmentPath = (segmentIndex: number) => {
    const startAngle = segmentIndex * 90 - 90 // Start from top (-90°), go clockwise
    const endAngle = startAngle + 90
    const outerRadius = 320
    const innerRadius = 180
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500

    const startAngleRad = (startAngle * Math.PI) / 180
    const endAngleRad = (endAngle * Math.PI) / 180

    const x1 = centerX + outerRadius * Math.cos(startAngleRad)
    const y1 = centerY + outerRadius * Math.sin(startAngleRad)
    const x2 = centerX + outerRadius * Math.cos(endAngleRad)
    const y2 = centerY + outerRadius * Math.sin(endAngleRad)

    const x3 = centerX + innerRadius * Math.cos(endAngleRad)
    const y3 = centerY + innerRadius * Math.sin(endAngleRad)
    const x4 = centerX + innerRadius * Math.cos(startAngleRad)
    const y4 = centerY + innerRadius * Math.sin(startAngleRad)

    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 0 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 0 0 ${x4} ${y4} Z`
  }

  // Calculate text position for each segment
  const getNumberPosition = (segmentIndex: number) => {
    const angle = segmentIndex * 90 - 45 // Middle of each segment
    const radius = 250 // Middle of the thick ring
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500
    const angleRad = (angle * Math.PI) / 180
    const x = centerX + radius * Math.cos(angleRad)
    const y = centerY + radius * Math.sin(angleRad)
    return { x, y }
  }

  // Position titles outside the ring segments
  const getExternalTitlePosition = (segmentIndex: number) => {
    const angle = segmentIndex * 90 - 45 // Middle of each segment
    const radius = 420 // Further outside the ring (increased from 380)
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500
    const angleRad = (angle * Math.PI) / 180
    const x = centerX + radius * Math.cos(angleRad)
    const y = centerY + radius * Math.sin(angleRad)
    return { x, y }
  }

  return (
    <section className="relative h-screen w-full overflow-hidden" style={{ backgroundColor: 'var(--color-black)' }}>
      {/* Background Video */}
      <div className="absolute inset-0">
        <div
          className="h-full w-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/placeholder.svg?height=1080&width=1920&text=Background+Video')",
          }}
        />
        <div className="absolute inset-0 video-overlay-light" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center px-8 md:px-16 lg:px-24">
        <div className="flex items-center justify-between w-full max-w-7xl mx-auto">
          {/* Left Side - Ring */}
          <div className="flex-shrink-0">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center" style={{ color: 'var(--color-white)' }}>The Science</h2>
            <svg
              width="1000"
              height="1000"
              viewBox="0 0 1000 1000"
              className="w-[450px] h-[450px] md:w-[600px] md:h-[600px] lg:w-[700px] lg:h-[700px]"
            >
              {segments.map((segment, index) => {
                const numberPos = getNumberPosition(index)
                const externalTitlePos = getExternalTitlePosition(index)
                const isActive = activeSegment === segment.number

                return (
                  <g key={segment.number}>
                    {/* Segment Path */}
                    <path
                      d={createSegmentPath(index)}
                      fill={isActive ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.15)"}
                      stroke="white"
                      strokeWidth="2"
                      className="diamond-segment cursor-pointer hover:fill-white/30"
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    />

                    {/* Segment Number */}
                    <text
                      x={numberPos.x}
                      y={numberPos.y}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className={`text-4xl md:text-5xl font-bold cursor-pointer transition-colors duration-300 ${
                        isActive ? "fill-black" : "fill-white"
                      }`}
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    >
                      {segment.number}
                    </text>

                    {/* External Phase Title - Outside Ring */}
                    <text
                      x={externalTitlePos.x}
                      y={externalTitlePos.y}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className={`text-xl md:text-2xl lg:text-3xl font-bold cursor-pointer transition-colors duration-300 ${
                        isActive ? "fill-white" : "fill-white/90"
                      }`}
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    >
                      {segment.title === "Mitochondrial Genesis" ? (
                        <>
                          <tspan x={externalTitlePos.x} dy="-0.5em">Mitochondrial</tspan>
                          <tspan x={externalTitlePos.x} dy="1em">Genesis</tspan>
                        </>
                      ) : (
                        segment.title
                      )}
                    </text>
                  </g>
                )
              })}

              {/* Center Circle */}
              <circle
                cx="500"
                cy="500"
                r="180"
                fill="rgba(0, 0, 0, 0.3)"
                stroke="white"
                strokeWidth="2"
                opacity="0.5"
              />
            </svg>
          </div>

          {/* Right Side - Information Panel */}
          <div className="flex-1 ml-8 md:ml-16 max-w-lg">
            {activeSegment ? (
              <div className="diamond-info-panel">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'var(--color-white)' }}>
                    <span className="text-2xl font-bold" style={{ color: 'var(--color-black)' }}>{activeSegment}</span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold">{segments[activeSegment - 1].title}</h3>
                </div>
                <p className="text-lg leading-relaxed mb-6" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>{segments[activeSegment - 1].description}</p>
                <div className="space-y-4 mb-6">
                  <div className="flex items-center text-sm" style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                    <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}></div>
                    <span>Phase {activeSegment} of 4</span>
                  </div>
                  <div className="w-full rounded-full h-2" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                    <div
                      className="h-2 rounded-full transition-all duration-500"
                      style={{
                        backgroundColor: 'var(--color-white)',
                        width: `${(activeSegment / 4) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                {/* Navigation Buttons - Always Visible */}
                <div className="grid grid-cols-4 gap-2">
                  {segments.map((segment) => (
                    <button
                      key={segment.number}
                      onClick={() => setActiveSegment(segment.number)}
                      className={`p-3 rounded-lg transition-all duration-300 border ${
                        activeSegment === segment.number
                          ? "border-white"
                          : "hover:border-white/40"
                      }`}
                      style={{
                        backgroundColor: activeSegment === segment.number
                          ? 'var(--color-white)'
                          : 'rgba(255, 255, 255, 0.1)',
                        color: activeSegment === segment.number
                          ? 'var(--color-black)'
                          : 'var(--color-white)',
                        borderColor: activeSegment === segment.number
                          ? 'var(--color-white)'
                          : 'rgba(255, 255, 255, 0.2)'
                      }}
                      onMouseEnter={(e) => {
                        if (activeSegment !== segment.number) {
                          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeSegment !== segment.number) {
                          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
                        }
                      }}
                    >
                      <div className="text-center">
                        <div className="font-bold text-lg mb-1">{segment.number}</div>
                        <div className="text-xs font-medium">{segment.title}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="diamond-info-panel-inactive">
                <h3 className="text-2xl md:text-3xl font-bold mb-4">The 4-Phase Science</h3>
                <p className="text-lg leading-relaxed mb-6" style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  Vitaliti Air&apos;s precision altitude training follows a scientifically-proven 4-phase process that optimizes your cellular function. Click on any segment to explore the science behind each phase.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  {segments.map((segment) => (
                    <button
                      key={segment.number}
                      onClick={() => setActiveSegment(segment.number)}
                      className="text-left p-4 rounded-lg transition-colors border"
                      style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        borderColor: 'rgba(255, 255, 255, 0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)'
                      }}
                    >
                      <div className="flex items-center mb-2">
                        <span className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                          {segment.number}
                        </span>
                        <span className="font-semibold">{segment.title}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
} 