"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { getSectionContent, WaitlistCTAContent } from "@/lib/content"

export function WaitlistCTASection() {
  const content = getSectionContent(8) as WaitlistCTAContent
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      // Handle email submission here
      console.log("Email submitted:", email)
      setIsSubmitted(true)
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail("")
      }, 3000)
    }
  }

  return (
    <section className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center waitlist-gradient">
      <div className="max-w-4xl mx-auto w-full text-center">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6" style={{ color: 'var(--color-white)' }}>
          {content.headline}
        </h2>
        
        <p className="text-lg md:text-xl mb-12 max-w-2xl mx-auto" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>
          {content.subheadline}
        </p>

        {!isSubmitted ? (
          <form onSubmit={handleSubmit} className="max-w-md mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all"
                required
              />
              <Button 
                type="submit" 
                size="lg" 
                className="waitlist-cta-button px-8 py-3 whitespace-nowrap"
              >
                {content.button}
              </Button>
            </div>
          </form>
        ) : (
          <div className="max-w-md mx-auto mb-8">
            <div className="success-message">
              <div className="success-icon mb-4">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2" style={{ color: 'var(--color-white)' }}>
                Thank you for joining!
              </h3>
              <p style={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                You&apos;re now on the waitlist. We&apos;ll be in touch soon!
              </p>
            </div>
          </div>
        )}

        <p className="text-sm" style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          {content.text}
        </p>

        {/* Decorative Elements */}
        <div className="decorative-elements">
          <div className="floating-element element-1"></div>
          <div className="floating-element element-2"></div>
          <div className="floating-element element-3"></div>
        </div>
      </div>
      
      <style jsx>{`
        .waitlist-gradient {
          background: linear-gradient(135deg, 
            var(--color-brand-blue-600) 0%, 
            var(--color-brand-blue-700) 50%, 
            var(--color-brand-blue-800) 100%
          );
          position: relative;
          overflow: hidden;
        }
        
        .waitlist-gradient::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
          pointer-events: none;
        }
        
        .waitlist-cta-button {
          background: var(--color-white);
          color: var(--color-brand-blue-600);
          border: none;
          font-weight: 600;
          transition: all 0.3s ease;
        }
        
        .waitlist-cta-button:hover {
          background: rgba(255, 255, 255, 0.9);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .success-message {
          padding: 2rem;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 1rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .success-icon {
          display: flex;
          justify-content: center;
          color: var(--color-green-400);
        }
        
        .decorative-elements {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          overflow: hidden;
        }
        
        .floating-element {
          position: absolute;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          animation: float 6s ease-in-out infinite;
        }
        
        .element-1 {
          width: 100px;
          height: 100px;
          top: 20%;
          left: 10%;
          animation-delay: 0s;
        }
        
        .element-2 {
          width: 60px;
          height: 60px;
          top: 60%;
          right: 15%;
          animation-delay: 2s;
        }
        
        .element-3 {
          width: 80px;
          height: 80px;
          bottom: 20%;
          left: 20%;
          animation-delay: 4s;
        }
        
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.5;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 0.8;
          }
        }
        
        @media (max-width: 640px) {
          .floating-element {
            display: none;
          }
        }
      `}</style>
    </section>
  )
}
