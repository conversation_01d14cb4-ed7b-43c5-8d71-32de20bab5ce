"use client"

import { useState } from "react"
import Image from "next/image"

interface TeamMember {
  id: number
  name: string
  title: string
  photo: string
}

interface FlipCardProps {
  partner: TeamMember
}

export function FlipCard({ partner }: FlipCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className="team-card flex flex-col items-center text-center space-y-4"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Circular Profile Picture */}
      <div className="relative">
        <div className="team-card-avatar">
          <Image
            src={partner.photo}
            alt={`${partner.name} photo`}
            width={160}
            height={160}
            className="w-full h-full object-cover"
          />
        </div>
        {/* Subtle hover effect ring */}
        <div className={`team-card-hover-ring ${
          isHovered ? "active" : ""
        }`} />
      </div>

      {/* Name */}
      <h3 className="text-xl md:text-2xl font-bold leading-tight" style={{ color: 'var(--color-gray-900)' }}>
        {partner.name}
      </h3>

      {/* Professional Title */}
      <p className="text-sm md:text-base leading-relaxed max-w-sm" style={{ color: 'var(--color-gray-600)' }}>
        {partner.title}
      </p>
    </div>
  )
}