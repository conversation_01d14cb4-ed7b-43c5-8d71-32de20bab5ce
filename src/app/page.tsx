"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FlipCard } from "@/components/flip-card"
import { DiamondSection } from "@/components/diamond-section"
import { InteractiveImageSection } from "@/components/interactive-image-section"

const videoData = [
  {
    id: 1,
    title: "Metabolic Transformation",
    subtitle: "Revolutionary altitude training technology that enhances insulin sensitivity and glucose regulation without traditional exercise",
    videoUrl: "/videos/hero-video-1.mp4", // ← Your first video
    isVideo: true,
    number: "01",
    statistic: "33%",
    statType: "improvement",
    statMetric: "Insulin sensitivity",
  },
  {
    id: 2,
    title: "Boundless Energy",
    subtitle: "Experience dramatic fatigue reduction and sustained vitality through precision altitude exposure protocols",
    videoUrl: "/videos/hero-video-2.mp4", // ← Your second video
    isVideo: true,
    number: "02",
    statistic: "30%",
    statType: "reduction",
    statMetric: "Fatigue",
  },
  {
    id: 3,
    title: "Cardiovascular Excellence",
    subtitle: "Unlock your body's peak aerobic capacity and endurance potential with cutting-edge hypoxic conditioning",
    videoUrl: "/videos/hero-video-4.mp4", // ← Switched: was hero-video-3
    isVideo: true,
    number: "03",
    statistic: "20%",
    statType: "improvement",
    statMetric: "Vo2 max",
  },
  {
    id: 4,
    title: "Peak Performance",
    subtitle: "Achieve unprecedented physical capacity improvements through scientifically-proven altitude training methods",
    videoUrl: "/videos/hero-video-3.mp4", // ← Switched: was hero-video-4
    isVideo: true,
    number: "04",
    statistic: "24%",
    statType: "improvement",
    statMetric: "Physical capacity",
  },
]

const teamData = [
  {
    id: 1,
    name: "Brian Kennedy, PhD",
    title: "Director at Centre for Healthy Longevity at National University of Singapore",
    photo: "/images/brian-kennedy.jpeg",
  },
  {
    id: 2,
    name: "Nathan Price, PhD", 
    title: "Professor and Co-Director, Center for Human Healthspan at Buck Institute for Research on Aging",
    photo: "/images/nathan-price.jpeg",
  },
  {
    id: 3,
    name: "Stanley Rockson, MD",
    title: "Chief of Consultative Cardiology at Stanford University",
    photo: "/images/stanley-rockson.jpg",
  },
]

export default function VideoLandingPage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % videoData.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  // Control video playback when slide changes
  useEffect(() => {
    videoRefs.current.forEach((video, index) => {
      if (video) {
        if (index === currentSlide && videoData[index].isVideo) {
          // Play the current video
          video.currentTime = 0 // Restart from beginning
          video.play().catch((error) => {
            console.log("Video play failed:", error)
          })
        } else {
          // Pause all other videos
          video.pause()
        }
      }
    })
  }, [currentSlide])

  const handleDotClick = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const currentVideo = videoData[currentSlide]

  return (
    <div className="relative w-full">
      {/* Video Hero Section - Full Screen */}
      <section className="relative h-screen w-full overflow-hidden" style={{ backgroundColor: 'var(--color-black)' }}>
        {/* Background Videos */}
        <div className="absolute inset-0">
          {videoData.map((video, index) => (
            <div
              key={video.id}
              className={`absolute inset-0 transition-opacity ${
                index === currentSlide ? "opacity-100" : "opacity-0"
              }`}
              style={{ transitionDuration: 'var(--duration-1000)' }}
            >
              {/* Render actual video element or background image */}
              {video.isVideo ? (
                <video
                  ref={(el) => {
                    videoRefs.current[index] = el
                  }}
                  className="h-full w-full object-cover"
                  muted
                  loop
                  playsInline
                  preload="metadata"
                >
                  <source src={video.videoUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <div
                  className="h-full w-full bg-cover bg-center bg-no-repeat"
                  style={{
                    backgroundImage: `url('${video.videoUrl}')`,
                  }}
                />
              )}
              {/* Video overlay for better text readability */}
              <div className="absolute inset-0 video-overlay" />
            </div>
          ))}
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 flex h-full items-center justify-between px-8 md:px-16 lg:px-24">
          {/* Left Content */}
          <div className="max-w-2xl" style={{ color: 'var(--color-white)' }}>
            <h1 className="mb-4 text-4xl font-bold leading-tight md:text-5xl lg:text-6xl">{currentVideo.title}</h1>
            <p className="mb-8 text-lg leading-relaxed md:text-xl lg:text-2xl" style={{ color: 'var(--color-gray-200)' }}>{currentVideo.subtitle}</p>
            <Button size="lg" className="btn-cta">
              Sign Up Now
            </Button>
          </div>

          {/* Right Content - Statistics Display */}
          <div className="hidden md:block" style={{ color: 'var(--color-white)' }}>
            <div className="stat-display">
              <div className="stat-number">{currentVideo.statistic}</div>
              <div className="stat-type">
                {currentVideo.statType}
              </div>
              <div className="stat-metric">
                {currentVideo.statMetric}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Dots - Bottom Right */}
        <div className="absolute bottom-8 right-8 z-20 flex space-x-3">
          {videoData.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`nav-dot ${
                index === currentSlide ? "active" : "inactive"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="progress-bar-container">
          <div className="progress-bar-track">
            <div
              className="progress-bar-fill"
              style={{
                width: `${((currentSlide + 1) / videoData.length) * 100}%`,
              }}
            />
          </div>
        </div>

        {/* Mobile Statistics Display */}
        <div className="absolute top-8 right-8 z-20 md:hidden text-right" style={{ color: 'var(--color-white)' }}>
          <div className="text-4xl font-light opacity-90">{currentVideo.statistic}</div>
          <div className="text-sm font-medium opacity-80">{currentVideo.statType}</div>
          <div className="text-xs opacity-70">{currentVideo.statMetric}</div>
        </div>
      </section>

      {/* Partners Grid Section - Second Section */}
      <section className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center" style={{ backgroundColor: 'var(--color-gray-50)' }}>
        <div className="max-w-6xl mx-auto w-full">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: 'var(--color-gray-900)' }}>Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {teamData.map((partner) => (
              <FlipCard key={partner.id} partner={partner} />
            ))}
          </div>
        </div>
      </section>

      {/* Diamond Section - Third Section */}
      <DiamondSection />

      {/* Interactive Image Section - Fourth Section */}
      <InteractiveImageSection />
    </div>
  )
}
