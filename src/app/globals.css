@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Base semantic colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  /* Vitaliti Air Brand Colors */
  --color-brand-blue-50: var(--brand-blue-50);
  --color-brand-blue-100: var(--brand-blue-100);
  --color-brand-blue-200: var(--brand-blue-200);
  --color-brand-blue-300: var(--brand-blue-300);
  --color-brand-blue-400: var(--brand-blue-400);
  --color-brand-blue-500: var(--brand-blue-500);
  --color-brand-blue-600: var(--brand-blue-600);
  --color-brand-blue-700: var(--brand-blue-700);
  --color-brand-blue-800: var(--brand-blue-800);
  --color-brand-blue-900: var(--brand-blue-900);

  /* Gray Scale */
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  /* White and Black */
  --color-white: var(--white);
  --color-black: var(--black);

  /* Video Overlay Colors */
  --color-video-overlay: var(--video-overlay);
  --color-video-overlay-light: var(--video-overlay-light);

  /* Interactive States */
  --color-hover-blue: var(--hover-blue);
  --color-hover-blue-light: var(--hover-blue-light);

  /* Typography */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-8xl: 6rem;
  --text-9xl: 8rem;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Animation Durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* Z-Index Scale */
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;

  /* Component Specific Sizes */
  --avatar-sm: 2rem;
  --avatar-md: 2.5rem;
  --avatar-lg: 3rem;
  --avatar-xl: 4rem;

  /* Chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Sidebar colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  /* Base Configuration */
  --radius: 0.625rem;

  /* Semantic Colors (shadcn/ui) */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);

  /* Vitaliti Air Brand Colors (Blue Palette) */
  --brand-blue-50: #eff6ff;
  --brand-blue-100: #dbeafe;
  --brand-blue-200: #bfdbfe;
  --brand-blue-300: #93c5fd;
  --brand-blue-400: #60a5fa;
  --brand-blue-500: #3b82f6;
  --brand-blue-600: #2563eb;
  --brand-blue-700: #1d4ed8;
  --brand-blue-800: #1e40af;
  --brand-blue-900: #1e3a8a;

  /* Red Palette (for problem indicators) */
  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-300: #fca5a5;
  --color-red-400: #f87171;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;

  /* Green Palette (for benefits/success) */
  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-300: #86efac;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-800: #166534;
  --color-green-900: #14532d;

  /* Gray Scale (matching current design) */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Pure Colors */
  --white: #ffffff;
  --black: #000000;

  /* Video Overlay Colors */
  --video-overlay: rgba(0, 0, 0, 0.3);
  --video-overlay-light: rgba(0, 0, 0, 0.5);

  /* Interactive States */
  --hover-blue: #60a5fa;
  --hover-blue-light: #bfdbfe;

  /* Chart colors */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Sidebar colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Semantic Colors (shadcn/ui) */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);

  /* Vitaliti Air Brand Colors (Dark Mode) */
  --brand-blue-50: #1e3a8a;
  --brand-blue-100: #1e40af;
  --brand-blue-200: #1d4ed8;
  --brand-blue-300: #2563eb;
  --brand-blue-400: #3b82f6;
  --brand-blue-500: #60a5fa;
  --brand-blue-600: #93c5fd;
  --brand-blue-700: #bfdbfe;
  --brand-blue-800: #dbeafe;
  --brand-blue-900: #eff6ff;

  /* Gray Scale (Dark Mode) */
  --gray-50: #111827;
  --gray-100: #1f2937;
  --gray-200: #374151;
  --gray-300: #4b5563;
  --gray-400: #6b7280;
  --gray-500: #9ca3af;
  --gray-600: #d1d5db;
  --gray-700: #e5e7eb;
  --gray-800: #f3f4f6;
  --gray-900: #f9fafb;

  /* Pure Colors (same in dark mode) */
  --white: #ffffff;
  --black: #000000;

  /* Video Overlay Colors (same in dark mode) */
  --video-overlay: rgba(0, 0, 0, 0.3);
  --video-overlay-light: rgba(0, 0, 0, 0.5);

  /* Interactive States (adjusted for dark mode) */
  --hover-blue: #93c5fd;
  --hover-blue-light: #60a5fa;

  /* Chart colors */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Sidebar colors */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom 3D flip card styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  backface-visibility: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* Vitaliti Air Custom Utility Classes */

/* Video Section Utilities */
.video-overlay {
  background-color: var(--color-video-overlay);
}

.video-overlay-light {
  background-color: var(--color-video-overlay-light);
}

/* Team Card Utilities */
.team-card {
  @apply bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 rounded-xl p-6;
}

.team-card-avatar {
  @apply w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden border-4 border-gray-200 shadow-lg;
}

.team-card-hover-ring {
  @apply absolute inset-0 rounded-full border-4 transition-all duration-300;
}

.team-card-hover-ring.active {
  border-color: var(--color-brand-blue-400);
  box-shadow: 0 10px 15px -3px var(--color-brand-blue-200);
}

/* Interactive Hotspot Utilities */
.hotspot-dot {
  @apply relative w-8 h-8 rounded-full shadow-lg flex items-center justify-center;
  background-color: var(--color-brand-blue-600);
}

.hotspot-dot-inner {
  @apply w-3 h-3 rounded-full;
  background-color: var(--color-white);
}

.hotspot-ring-outer {
  @apply absolute inset-0 w-8 h-8 rounded-full animate-ping;
  background-color: var(--color-brand-blue-500);
  opacity: 0.3;
}

.hotspot-ring-middle {
  @apply absolute inset-1 w-6 h-6 rounded-full animate-pulse;
  background-color: var(--color-brand-blue-500);
  opacity: 0.5;
}

.hotspot-tooltip {
  @apply bg-gray-900/90 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap backdrop-blur-sm;
}

/* Modal Utilities */
.modal-overlay {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4 backdrop-blur-sm;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  @apply bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[80vh] overflow-y-auto;
}

/* Diamond Section Utilities */
.diamond-ring {
  filter: drop-shadow(0 0 10px var(--color-brand-blue-500));
  opacity: 0.5;
}

.diamond-segment {
  transition: all var(--duration-300) ease;
}

.diamond-segment:hover {
  filter: drop-shadow(0 0 15px var(--color-brand-blue-600));
  opacity: 0.8;
}

.diamond-info-panel {
  @apply bg-white/10 backdrop-blur-md text-white p-8 rounded-xl border border-white/20;
}

.diamond-info-panel-inactive {
  @apply bg-white/5 backdrop-blur-md text-white p-8 rounded-xl border border-white/10;
}

/* Navigation Utilities */
.nav-dot {
  @apply h-3 w-3 rounded-full border-2 border-white transition-all duration-300;
}

.nav-dot.active {
  @apply bg-white scale-110;
}

.nav-dot.inactive {
  @apply bg-transparent hover:bg-white/50;
}

/* Progress Bar Utilities */
.progress-bar-container {
  @apply absolute bottom-0 left-0 right-0 z-20;
}

.progress-bar-track {
  @apply h-1;
  background-color: rgba(255, 255, 255, 0.2);
}

.progress-bar-fill {
  @apply h-full transition-all duration-300 ease-linear;
  background-color: var(--color-white);
}

/* Statistics Display Utilities */
.stat-display {
  @apply text-right;
}

.stat-number {
  @apply text-8xl font-light opacity-90 lg:text-9xl;
}

.stat-type {
  @apply mt-2 text-lg font-medium opacity-80;
}

.stat-metric {
  @apply text-base opacity-70 mt-1;
}

/* Button Variants */
.btn-cta {
  @apply px-8 py-3 text-lg font-semibold;
  background-color: var(--color-white);
  color: var(--color-black);
}

.btn-cta:hover {
  background-color: var(--color-gray-100);
}

/* Custom 3D flip card styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  backface-visibility: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* Interactive hotspot pulse animation */
.hotspot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}
