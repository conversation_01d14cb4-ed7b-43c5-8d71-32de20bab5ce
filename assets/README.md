# Shared Assets

This directory contains assets that are shared across different design versions of the Vitaliti Air website.

## Structure

```
assets/
├── images/
│   └── shared/
│       ├── nathan-price.jpeg
│       ├── stanley-rockson.jpg
│       └── brian-kennedy.jpeg
└── README.md
```

## Usage

### Team Photos
The team member photos are stored here for reuse across different design versions:
- `nathan-price.jpeg` - <PERSON>, PhD (Chief Science Officer)
- `stanley-rockson.jpg` - <PERSON>, MD (Chief Medical Advisor)  
- `brian-kennedy.jpeg` - <PERSON>, PhD (Scientific Advisor, Longevity)

### How to Use
Reference these assets in your components:
```jsx
import Image from "next/image"

<Image 
  src="/assets/images/shared/nathan-price.jpeg" 
  alt="<PERSON>, <PERSON>"
  width={96} 
  height={96} 
/>
```

## Adding New Shared Assets

When adding assets that will be used across multiple design versions:

1. Add the file to the appropriate subdirectory
2. Update this README with the new asset information
3. Consider removing the asset from version-specific directories to avoid duplication

## Version-Specific Assets

Assets that are specific to a particular design version should remain in their respective directories:
- `public/images/` - Current design assets
- `legacy/public/images/` - Original design assets 