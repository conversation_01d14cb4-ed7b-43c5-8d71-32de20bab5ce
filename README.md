# Vitaliti Air Website

The official website for Vitaliti Air - revolutionary altitude training technology that delivers the benefits of exercise without exercise.

## Project Structure

This repository maintains multiple design versions using a comprehensive preservation strategy:

```
vitaliti-air-website/
├── src/                 # Current design source code
├── public/              # Current design assets
├── legacy/              # Original design archive
│   ├── src/            # Original source code
│   ├── public/         # Original assets
│   └── README.md       # Legacy documentation
├── assets/              # Shared assets across versions
│   └── images/shared/  # Reusable images (team photos, etc.)
└── README.md           # This file
```

## Design Preservation Strategy

The original design is preserved in three ways:

1. **Git Tag**: `v1.0-original-design` - Historical reference point
2. **Git Branch**: `legacy-design` - Living codebase for maintenance
3. **Legacy Folder**: `/legacy/` - Instant access without git commands

## Getting Started

This project uses **pnpm** as the package manager for improved performance and disk efficiency.

### Prerequisites
```bash
# Install pnpm globally if not already installed
npm install -g pnpm
```

### Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the current design.

### Available Scripts
```bash
pnpm run dev      # Start development server with Turbopack
pnpm run build    # Build for production
pnpm run start    # Start production server
pnpm run lint     # Run ESLint
pnpm run clean    # Remove node_modules, .next, and pnpm-lock.yaml
```

### Accessing Previous Design

#### Quick Reference
Browse the `/legacy/` folder to see the original implementation.

#### Restore Original Design
```bash
# Option 1: Use git branch
git checkout legacy-design

# Option 2: Use git tag
git checkout v1.0-original-design

# Option 3: Copy from legacy folder
cp -r legacy/src/* src/
cp -r legacy/public/* public/
```

## Shared Assets

Team photos and other reusable assets are stored in `/assets/images/shared/` for easy access across design versions.

## Technology Stack

- **Framework**: Next.js 15.3.4
- **Package Manager**: pnpm 10.12.1+
- **Styling**: Tailwind CSS 4 with centralized CSS variable theming
- **Components**: Radix UI + shadcn/ui
- **Language**: TypeScript
- **Icons**: Lucide React

## Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!
